# frozen_string_literal: true

module Api
  module V3
    class ResourceController < BaseController
      # doorkeeper scopes usage: https://github.com/doorkeeper-gem/doorkeeper/wiki/Using-Scopes
      before_action :validate_token_client
      before_action -> { doorkeeper_authorize! :read, :admin }
      before_action -> { doorkeeper_authorize! :write, :admin }, if: :write_request?

      # optional authorization if using a user token instead of app token
      before_action :authorize_spree_user

      after_action :set_pagination_headers, only: :index

      def index
        render_serialized_payload { serialize_collection(paginated_collection) }
      end

      def show
        render_serialized_payload { serialize_resource(resource) }
      end

      def create
        @resource = model_class.new(permitted_resource_params)
        ensure_current_store(@resource)

        if @resource.save
          render_serialized_payload(:created) { serialize_resource(@resource) }
        else
          render_error_payload(@resource.errors)
        end
      end

      def update
        debugger
        resource.assign_attributes(permitted_resource_params)
        ensure_current_store(resource)

        if resource.save
          render_serialized_payload { serialize_resource(resource) }
        else
          render_error_payload(resource.errors)
        end
      end

      def destroy
        if resource.destroy
          head :no_content
        else
          render_error_payload(resource.errors)
        end
      end

      protected

      def model_class
        nil
      end

      # Scope methods
      def parent_scope
        model_class.for_store(current_store)
      end

      def scope(skip_cancancan: false)
        base_scope = parent_scope
        base_scope = base_scope.accessible_by(current_ability, :show) unless skip_cancancan
        base_scope = base_scope.includes(scope_includes) if scope_includes.any? && action_name == 'index'
        base_scope = model_class.include?(Spree::TranslatableResource) ? base_scope.i18n : base_scope
        base_scope
      end

      def scope_includes
        []
      end

      # overwriting to utilize ransack gem for filtering
      # https://github.com/activerecord-hackery/ransack#search-matchers
      def collection
        @collection ||= scope.ransack(params[:filter]).result
      end

      def sorted_collection
        @sorted_collection ||= Spree::BaseSorter.new(collection, params, allowed_sort_attributes).call
      end

      def paginated_collection
        # @paginated_collection ||= collection_paginator.new(sorted_collection, params).call

        @paginated_collection ||= begin
          collection = sorted_collection
          if params[:page]
            collection = sorted_collection.page(params[:page]).per(params[:per_page])
          end
          collection
        end
      end

      def resource
        @resource ||= scope.find(params[:id])
      end

      # Serializer methods
      def resource_serializer
        serializer_base_name = model_class.to_s.sub('Spree::', '')
        "::Api::V3::#{serializer_base_name}Serializer".constantize
      end

      def collection_serializer
        resource_serializer
      end

      def serialize_collection(collection, serializer = collection_serializer, options = {})
        serializer.render(collection, **serializer_params.merge(options))
      end

      def serialize_resource(resource, serializer = resource_serializer, options = {})
        options = options.merge(base_url: request.base_url)
        serializer.render(resource, **serializer_params.merge(options))
      end

      # Override method from BaseController because the original one calls `dookreeper_authorize`
      # which breaks our application authorizations defined on top of this controller
      def current_user
        return unless doorkeeper_token
        return if doorkeeper_token.resource_owner_id.nil?
        return @current_user if @current_user

        @current_user ||= doorkeeper_token.resource_owner
      end

      def access_denied(exception)
        access_denied_401(exception)
      end

      def validate_token_client
        return if doorkeeper_token.nil?

        raise Doorkeeper::Errors::DoorkeeperError if doorkeeper_token.application.nil?
      end

      # if using a user oAuth token we need to check CanCanCan abilities
      # defined in https://github.com/spree/spree/blob/master/core/app/models/spree/ability.rb
      def authorize_spree_user
        return if current_user.nil?

        case action_name
        when 'create'
          spree_authorize! :create, model_class
        when 'destroy'
          spree_authorize! :destroy, resource
        when 'index'
          spree_authorize! :read, model_class
        when 'show'
          return if model_class == Spree::RecommendedPrice

          spree_authorize! :read, resource
        else
          return if (model_class == Spree::BlogPost && action_name == 'upload_images') || (model_class == Spree::BlogPost && action_name == 'remove_images') || action_name == 'list_senders'

          spree_authorize! :update, resource
        end
      end

      def model_param_name
        model_class.to_s.demodulize.underscore
      end

      def spree_permitted_attributes
        store_ids = if model_class.method_defined?(:stores)
          [{store_ids: []}]
        else
          []
        end

        model_class.json_api_permitted_attributes + store_ids + metadata_params
      end

      def metadata_params
        if model_class.include?(Spree::Metadata)
          [{public_metadata: {}, private_metadata: {}}]
        else
          []
        end
      end

      def permitted_resource_params
        params.require(model_param_name).permit(spree_permitted_attributes)
      end

      def default_sort_attributes
        [:id, :name, :slug, :number, :position, :updated_at, :created_at, :deleted_at]
      end

      def allowed_sort_attributes
        (default_sort_attributes << spree_permitted_attributes).uniq.compact
      end

      def write_request?
        ['put', 'patch', 'post', 'delete'].include?(request.request_method.downcase)
      end

      def set_pagination_headers
        return if model_class.nil?

        scope = paginated_collection

        return unless scope.respond_to?(:total_pages)

        # request_params = request.query_parameters
        # url_without_params = request.original_url.slice(0..(request.original_url.index('?') - 1)) unless request_params.empty?
        # url_without_params ||= request.original_url

        # page = {}
        # page[:first] = 1 if scope.total_pages > 1 && !scope.first_page?
        # page[:last] = scope.total_pages  if scope.total_pages > 1 && !scope.last_page?
        # page[:next] = scope.current_page + 1 unless scope.last_page?
        # page[:prev] = scope.current_page - 1 unless scope.first_page?

        # pagination_links = []
        # page.each do |k, v|
        #   new_request_hash = request_params.merge({page: v})
        #   pagination_links << "<#{url_without_params}?#{new_request_hash.to_param}>; rel=\"#{k}\""
        # end
        # headers['Link'] = pagination_links.join(', ')
        headers['X-Page'] = scope.current_page
        headers['X-Total-Pages'] = scope.total_pages
        headers['X-Total-Count'] = scope.total_count
      end

      def set_permissions
        @permissions ||= current_user_permissions.find_permission
      end

      def current_user_permissions
        @current_user_permissions ||= UserPermissions::CurrentUserPermissions.new(spree_current_user)
      end
    end
  end
end
